{"version": 2, "entries": [{"package": "flux_interface", "rootUri": "../../flux_interface/", "packageUri": "lib/"}, {"package": "flux_localization", "rootUri": "../../flux_localization/", "packageUri": "lib/"}, {"package": "fstore", "rootUri": "../../../", "packageUri": "lib/"}, {"package": "tabby_flutter_inapp_sdk", "rootUri": "../../amrdev_payment/tabby_flutter_inapp_sdk/", "packageUri": "lib/"}, {"package": "tamara_flutter_sdk", "rootUri": "../../amrdev_payment/tamara_flutter_sdk-1.0.16/", "packageUri": "lib/"}, {"package": "flux_firebase", "rootUri": "../", "packageUri": "lib/"}]}