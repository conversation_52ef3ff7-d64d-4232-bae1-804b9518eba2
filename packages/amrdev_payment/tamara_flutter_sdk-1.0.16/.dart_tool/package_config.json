{"configVersion": 2, "packages": [{"name": "async", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "auto_size_text_field", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/auto_size_text_field-2.2.4", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "boolean_selector", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "characters", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "clock", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "collection", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "fake_async", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter", "rootUri": "file:///Users/<USER>/dev/flutter/packages/flutter", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "flutter_lints", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-5.0.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_test", "rootUri": "file:///Users/<USER>/dev/flutter/packages/flutter_test", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "flutter_web_plugins", "rootUri": "file:///Users/<USER>/dev/flutter/packages/flutter_web_plugins", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "fluttertoast", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.12", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "leak_tracker", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.7", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_flutter_testing", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.8", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_testing", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "lints", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/lints-5.1.1", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "matcher", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.16+1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "material_color_utilities", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "meta", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.15.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "path", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "permission_handler", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-11.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "permission_handler_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "permission_handler_apple", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "permission_handler_html", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "permission_handler_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "permission_handler_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "plugin_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "sky_engine", "rootUri": "file:///Users/<USER>/dev/flutter/bin/cache/pkg/sky_engine", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "source_span", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "stack_trace", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "stream_channel", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.2", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "string_scanner", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.3.0", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "term_glyph", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "test_api", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "vector_math", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "vm_service", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-14.3.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "webview_flutter", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter-4.9.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "webview_flutter_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_android-3.16.9", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "webview_flutter_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.1", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "webview_flutter_wkwebview", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.17.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "tamara_flutter_sdk", "rootUri": "../", "packageUri": "lib/", "languageVersion": "3.5"}], "generated": "2025-07-27T15:04:27.251159Z", "generator": "pub", "generatorVersion": "3.6.1", "flutterRoot": "file:///Users/<USER>/dev/flutter", "flutterVersion": "3.27.3", "pubCache": "file:///Users/<USER>/.pub-cache"}