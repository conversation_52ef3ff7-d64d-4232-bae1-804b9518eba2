{"version": 2, "entries": [{"package": "flux_firebase", "rootUri": "../packages/flux_firebase/", "packageUri": "lib/"}, {"package": "flux_interface", "rootUri": "../packages/flux_interface/", "packageUri": "lib/"}, {"package": "flux_localization", "rootUri": "../packages/flux_localization/", "packageUri": "lib/"}, {"package": "tabby_flutter_inapp_sdk", "rootUri": "../packages/amrdev_payment/tabby_flutter_inapp_sdk/", "packageUri": "lib/"}, {"package": "tamara_flutter_sdk", "rootUri": "../packages/amrdev_payment/tamara_flutter_sdk-1.0.16/", "packageUri": "lib/"}, {"package": "fstore", "rootUri": "../", "packageUri": "lib/"}]}